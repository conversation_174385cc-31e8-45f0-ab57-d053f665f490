import torch
import torch.nn as nn
import numpy as np
import pandas as pd
from torch.utils.data import DataLoader, TensorDataset
import os

from molecule_encoder import MoleculeEncoder, Feature_Size, smiles_to_sequence, Drug_Max_Length


def load_and_process_data(input_file='input_data.xlsx', smiles_column=None):
    """加载和处理分子数据"""
    try:
        data = pd.read_excel(input_file)
        print(f"成功加载数据，共 {len(data)} 条记录")
    except Exception as e:
        print(f"加载数据失败: {e}")
        return None, None, None

    # 自动识别SMILES列
    if smiles_column is None:
        possible_names = ['smiles', 'SMILES', 'Smiles', 'smi', 'SMI', 'molecule', 'structure']
        for col in data.columns:
            if any(name.lower() in col.lower() for name in possible_names):
                smiles_column = col
                break
        if smiles_column is None:
            smiles_column = data.columns[0]

    print(f"使用列 '{smiles_column}' 作为SMILES数据")

    # 处理SMILES数据
    smiles_sequences = []
    valid_indices = []

    for idx, smiles in enumerate(data[smiles_column]):
        if pd.isna(smiles) or smiles == '':
            continue
        try:
            sequence = smiles_to_sequence(str(smiles))
            smiles_sequences.append(sequence)
            valid_indices.append(idx)
        except Exception as e:
            print(f"处理SMILES '{smiles}' 时出错: {e}")
            continue

    if not smiles_sequences:
        print("没有有效的SMILES数据")
        return None, None, None

    # 创建虚拟图数据
    num_molecules = len(smiles_sequences)
    graph_data = np.zeros((num_molecules, Drug_Max_Length, Drug_Max_Length * 2 + 1))

    for i in range(num_molecules):
        np.fill_diagonal(graph_data[i, :, :Drug_Max_Length], 1.0)
        for j in range(min(Drug_Max_Length-1, 10)):
            graph_data[i, j, Drug_Max_Length + j + 1] = 1.0
            graph_data[i, j + 1, Drug_Max_Length + j] = 1.0
        graph_data[i, :10, -1] = np.random.randint(1, 20, 10)

    smiles_tensor = torch.from_numpy(np.array(smiles_sequences, dtype=int)).long()
    graph_tensor = torch.from_numpy(graph_data).float()
    valid_data = data.iloc[valid_indices].reset_index(drop=True)

    print(f"成功处理 {len(smiles_sequences)} 个SMILES分子")
    return smiles_tensor, graph_tensor, valid_data


def create_sample_data():
    """创建示例数据"""
    sample_smiles = ['CCO', 'CC(=O)O', 'c1ccccc1', 'CCN(CC)CC', 'CC(C)O']
    sample_data = pd.DataFrame({
        'molecule_id': [f'mol_{i+1:03d}' for i in range(len(sample_smiles))],
        'smiles': sample_smiles,
        'activity': np.random.choice([0, 1], len(sample_smiles))
    })
    sample_data.to_excel('input_data.xlsx', index=False)
    print("已创建示例数据文件: input_data.xlsx")


class MoleculeClassifier(nn.Module):
    """小分子分类器"""
    def __init__(self, input_dim=Feature_Size, num_classes=2):
        super(MoleculeClassifier, self).__init__()
        self.encoder = MoleculeEncoder()
        self.classifier = nn.Sequential(
            nn.Linear(input_dim, 512),
            nn.LeakyReLU(0.2),
            nn.Dropout(0.3),
            nn.Linear(512, 256),
            nn.LeakyReLU(0.2),
            nn.Dropout(0.3),
            nn.Linear(256, num_classes)
        )
    
    def forward(self, smiles_data, graph_data):
        features, smiles_features, graph_features = self.encoder(smiles_data, graph_data)
        output = self.classifier(features)
        return output, features, smiles_features, graph_features


def encode_molecules(input_file='input_data.xlsx', output_file='molecule_features.csv', 
                    smiles_column=None, batch_size=32):
    """
    对小分子进行编码
    
    Args:
        input_file: 输入Excel文件路径
        output_file: 输出特征文件路径
        smiles_column: SMILES列名（None时自动识别）
        batch_size: 批处理大小
    """
    
    # 检查CUDA可用性
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 加载和处理数据
    print("正在加载和处理数据...")
    smiles_tensor, graph_tensor, valid_data = load_and_process_data(input_file, smiles_column)
    
    if smiles_tensor is None:
        print("数据处理失败，程序退出")
        return None
    
    print(f"成功处理 {len(smiles_tensor)} 个分子")
    
    # 创建数据加载器
    dataset = TensorDataset(smiles_tensor, graph_tensor)
    dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=False)
    
    # 初始化模型
    model = MoleculeEncoder()
    model.to(device)
    model.eval()
    
    # 编码分子
    print("正在编码分子...")
    all_features = []
    all_smiles_features = []
    all_graph_features = []
    
    with torch.no_grad():
        for batch_idx, (smiles_batch, graph_batch) in enumerate(dataloader):
            smiles_batch = smiles_batch.to(device)
            graph_batch = graph_batch.to(device)
            
            features, smiles_features, graph_features = model(smiles_batch, graph_batch)
            
            all_features.append(features.cpu().numpy())
            all_smiles_features.append(smiles_features.cpu().numpy())
            all_graph_features.append(graph_features.cpu().numpy())
            
            if (batch_idx + 1) % 10 == 0:
                print(f"已处理 {(batch_idx + 1) * batch_size} / {len(smiles_tensor)} 个分子")
    
    # 合并特征
    combined_features = np.vstack(all_features)
    smiles_features_array = np.vstack(all_smiles_features)
    graph_features_array = np.vstack(all_graph_features)
    
    print(f"编码完成，特征维度: {combined_features.shape}")
    
    # 保存结果
    results_df = valid_data.copy()
    
    # 添加组合特征
    for i in range(combined_features.shape[1]):
        results_df[f'combined_feature_{i+1}'] = combined_features[:, i]
    
    # 添加SMILES特征
    for i in range(smiles_features_array.shape[1]):
        results_df[f'smiles_feature_{i+1}'] = smiles_features_array[:, i]
    
    # 添加图特征
    for i in range(graph_features_array.shape[1]):
        results_df[f'graph_feature_{i+1}'] = graph_features_array[:, i]
    
    # 保存到CSV文件
    results_df.to_csv(output_file, index=False)
    print(f"结果已保存到: {output_file}")
    
    # 保存特征摘要
    summary = {
        'total_molecules': len(smiles_tensor),
        'feature_dimension': combined_features.shape[1],
        'smiles_feature_dim': smiles_features_array.shape[1],
        'graph_feature_dim': graph_features_array.shape[1],
        'input_file': input_file,
        'output_file': output_file
    }
    
    summary_df = pd.DataFrame([summary])
    summary_file = output_file.replace('.csv', '_summary.csv')
    summary_df.to_csv(summary_file, index=False)
    print(f"处理摘要已保存到: {summary_file}")
    
    return results_df, combined_features, smiles_features_array, graph_features_array


def analyze_features(features, feature_names=None):
    """分析特征统计信息"""
    if feature_names is None:
        feature_names = [f'feature_{i+1}' for i in range(features.shape[1])]
    
    stats = {
        'feature': feature_names,
        'mean': np.mean(features, axis=0),
        'std': np.std(features, axis=0),
        'min': np.min(features, axis=0),
        'max': np.max(features, axis=0),
        'median': np.median(features, axis=0)
    }
    
    stats_df = pd.DataFrame(stats)
    return stats_df


def main():
    """主函数"""
    print("=== 小分子编码系统 ===")
    
    # 检查输入文件
    input_file = 'input_data.xlsx'
    if not os.path.exists(input_file):
        print(f"输入文件 {input_file} 不存在")
        print("正在创建示例数据...")
        create_sample_data()
    
    # 执行分子编码
    try:
        results_df, combined_features, smiles_features, graph_features = encode_molecules(
            input_file=input_file,
            output_file='molecule_features.csv',
            batch_size=32
        )
        
        if results_df is not None:
            print("\n=== 编码完成 ===")
            print(f"处理了 {len(results_df)} 个分子")
            print(f"组合特征维度: {combined_features.shape[1]}")
            print(f"SMILES特征维度: {smiles_features.shape[1]}")
            print(f"图特征维度: {graph_features.shape[1]}")
            
            # 分析特征统计
            print("\n=== 特征统计分析 ===")
            combined_stats = analyze_features(combined_features, 
                                            [f'combined_{i+1}' for i in range(combined_features.shape[1])])
            print("组合特征统计:")
            print(combined_stats.describe())
            
            # 保存统计信息
            combined_stats.to_csv('feature_statistics.csv', index=False)
            print("特征统计信息已保存到: feature_statistics.csv")
            
        else:
            print("编码失败")
            
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
