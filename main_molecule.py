import torch
import torch.nn as nn
import numpy as np
import pandas as pd
from torch.utils.data import DataLoader, TensorDataset
import os

from molecule_encoder import MoleculeEncoder, Feature_Size
from data_processor import MoleculeDataProcessor


class MoleculeClassifier(nn.Module):
    """小分子分类器"""
    def __init__(self, input_dim=Feature_Size, num_classes=2):
        super(MoleculeClassifier, self).__init__()
        self.encoder = MoleculeEncoder()
        self.classifier = nn.Sequential(
            nn.Linear(input_dim, 512),
            nn.LeakyReLU(0.2),
            nn.Dropout(0.3),
            nn.Linear(512, 256),
            nn.LeakyReLU(0.2),
            nn.Dropout(0.3),
            nn.Linear(256, num_classes)
        )
    
    def forward(self, smiles_data, graph_data):
        features, smiles_features, graph_features = self.encoder(smiles_data, graph_data)
        output = self.classifier(features)
        return output, features, smiles_features, graph_features


def encode_molecules(input_file='input_data.xlsx', output_file='molecule_features.csv', 
                    smiles_column=None, batch_size=32):
    """
    对小分子进行编码
    
    Args:
        input_file: 输入Excel文件路径
        output_file: 输出特征文件路径
        smiles_column: SMILES列名（None时自动识别）
        batch_size: 批处理大小
    """
    
    # 检查CUDA可用性
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 加载和处理数据
    print("正在加载和处理数据...")
    processor = MoleculeDataProcessor(input_file)
    smiles_tensor, graph_tensor, valid_data = processor.get_processed_data(smiles_column)
    
    if smiles_tensor is None:
        print("数据处理失败，程序退出")
        return None
    
    print(f"成功处理 {len(smiles_tensor)} 个分子")
    
    # 创建数据加载器
    dataset = TensorDataset(smiles_tensor, graph_tensor)
    dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=False)
    
    # 初始化模型
    model = MoleculeEncoder()
    model.to(device)
    model.eval()
    
    # 编码分子
    print("正在编码分子...")
    all_features = []
    all_smiles_features = []
    all_graph_features = []
    
    with torch.no_grad():
        for batch_idx, (smiles_batch, graph_batch) in enumerate(dataloader):
            smiles_batch = smiles_batch.to(device)
            graph_batch = graph_batch.to(device)
            
            features, smiles_features, graph_features = model(smiles_batch, graph_batch)
            
            all_features.append(features.cpu().numpy())
            all_smiles_features.append(smiles_features.cpu().numpy())
            all_graph_features.append(graph_features.cpu().numpy())
            
            if (batch_idx + 1) % 10 == 0:
                print(f"已处理 {(batch_idx + 1) * batch_size} / {len(smiles_tensor)} 个分子")
    
    # 合并特征
    combined_features = np.vstack(all_features)
    smiles_features_array = np.vstack(all_smiles_features)
    graph_features_array = np.vstack(all_graph_features)
    
    print(f"编码完成，特征维度: {combined_features.shape}")
    
    # 保存结果
    results_df = valid_data.copy()
    
    # 添加组合特征
    for i in range(combined_features.shape[1]):
        results_df[f'combined_feature_{i+1}'] = combined_features[:, i]
    
    # 添加SMILES特征
    for i in range(smiles_features_array.shape[1]):
        results_df[f'smiles_feature_{i+1}'] = smiles_features_array[:, i]
    
    # 添加图特征
    for i in range(graph_features_array.shape[1]):
        results_df[f'graph_feature_{i+1}'] = graph_features_array[:, i]
    
    # 保存到CSV文件
    results_df.to_csv(output_file, index=False)
    print(f"结果已保存到: {output_file}")
    
    # 保存特征摘要
    summary = {
        'total_molecules': len(smiles_tensor),
        'feature_dimension': combined_features.shape[1],
        'smiles_feature_dim': smiles_features_array.shape[1],
        'graph_feature_dim': graph_features_array.shape[1],
        'input_file': input_file,
        'output_file': output_file
    }
    
    summary_df = pd.DataFrame([summary])
    summary_file = output_file.replace('.csv', '_summary.csv')
    summary_df.to_csv(summary_file, index=False)
    print(f"处理摘要已保存到: {summary_file}")
    
    return results_df, combined_features, smiles_features_array, graph_features_array


def analyze_features(features, feature_names=None):
    """分析特征统计信息"""
    if feature_names is None:
        feature_names = [f'feature_{i+1}' for i in range(features.shape[1])]
    
    stats = {
        'feature': feature_names,
        'mean': np.mean(features, axis=0),
        'std': np.std(features, axis=0),
        'min': np.min(features, axis=0),
        'max': np.max(features, axis=0),
        'median': np.median(features, axis=0)
    }
    
    stats_df = pd.DataFrame(stats)
    return stats_df


def main():
    """主函数"""
    print("=== 小分子编码系统 ===")
    
    # 检查输入文件
    input_file = 'input_data.xlsx'
    if not os.path.exists(input_file):
        print(f"输入文件 {input_file} 不存在")
        print("正在创建示例数据...")
        from data_processor import create_sample_data
        create_sample_data()
        input_file = 'sample_input_data.xlsx'
    
    # 执行分子编码
    try:
        results_df, combined_features, smiles_features, graph_features = encode_molecules(
            input_file=input_file,
            output_file='molecule_features.csv',
            batch_size=32
        )
        
        if results_df is not None:
            print("\n=== 编码完成 ===")
            print(f"处理了 {len(results_df)} 个分子")
            print(f"组合特征维度: {combined_features.shape[1]}")
            print(f"SMILES特征维度: {smiles_features.shape[1]}")
            print(f"图特征维度: {graph_features.shape[1]}")
            
            # 分析特征统计
            print("\n=== 特征统计分析 ===")
            combined_stats = analyze_features(combined_features, 
                                            [f'combined_{i+1}' for i in range(combined_features.shape[1])])
            print("组合特征统计:")
            print(combined_stats.describe())
            
            # 保存统计信息
            combined_stats.to_csv('feature_statistics.csv', index=False)
            print("特征统计信息已保存到: feature_statistics.csv")
            
        else:
            print("编码失败")
            
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
