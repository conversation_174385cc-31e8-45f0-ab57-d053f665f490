# 安装和使用指南

## 环境要求

- Python 3.8+
- 推荐使用conda或虚拟环境

## 安装步骤

### 1. 创建虚拟环境（推荐）

```bash
# 使用conda
conda create -n molecule_encoder python=3.8
conda activate molecule_encoder

# 或使用venv
python -m venv molecule_encoder
# Windows
molecule_encoder\Scripts\activate
# Linux/Mac
source molecule_encoder/bin/activate
```

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

如果安装失败，可以手动安装：

```bash
pip install torch pandas numpy openpyxl scikit-learn tqdm
```

### 3. 准备数据

将您的数据保存为Excel文件 `input_data.xlsx`，确保包含SMILES列。

示例数据格式：
```
molecule_id | smiles        | other_columns
mol_001     | CCO           | ...
mol_002     | CC(=O)O       | ...
mol_003     | c1ccccc1      | ...
```

### 4. 运行程序

```bash
python main_molecule.py
```

## 输出文件

程序运行后会生成：

1. `molecule_features.csv` - 包含原始数据和编码特征
2. `molecule_features_summary.csv` - 处理摘要
3. `feature_statistics.csv` - 特征统计信息

## 常见问题

### Q: 没有input_data.xlsx文件怎么办？
A: 程序会自动创建示例数据文件 `sample_input_data.xlsx`

### Q: SMILES列名不标准怎么办？
A: 程序会自动识别包含smiles、molecule等关键词的列

### Q: 内存不足怎么办？
A: 可以在 `main_molecule.py` 中调整 `batch_size` 参数

### Q: 没有GPU怎么办？
A: 程序会自动检测并使用CPU运行

## 自定义配置

可以在 `molecule_encoder.py` 中修改以下参数：

- `Feature_Size`: 输出特征维度
- `Drug_Max_Length`: 最大分子长度
- `DSC_Kernel_Num`: 卷积核数量
- `DSC_Kernel_Size`: 卷积核大小

## 技术支持

如有问题，请检查：
1. Python版本是否符合要求
2. 依赖包是否正确安装
3. 输入数据格式是否正确
4. SMILES字符串是否有效
