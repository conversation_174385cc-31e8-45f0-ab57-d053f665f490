import pandas as pd
import numpy as np
import torch
from molecule_encoder import smiles_to_sequence, Drug_Max_Length, atom_dict


class MoleculeDataProcessor:
    """小分子数据处理器"""
    
    def __init__(self, input_file='input_data.xlsx'):
        self.input_file = input_file
        self.data = None
        
    def load_data(self):
        """加载Excel数据"""
        try:
            self.data = pd.read_excel(self.input_file)
            print(f"成功加载数据，共 {len(self.data)} 条记录")
            print(f"数据列: {list(self.data.columns)}")
            return True
        except Exception as e:
            print(f"加载数据失败: {e}")
            return False
    
    def get_smiles_column(self):
        """自动识别SMILES列"""
        if self.data is None:
            return None
            
        # 常见的SMILES列名
        possible_names = ['smiles', 'SMILES', 'Smiles', 'smi', 'SMI', 'molecule', 'structure']
        
        for col in self.data.columns:
            if any(name.lower() in col.lower() for name in possible_names):
                return col
                
        # 如果没有找到，返回第一列
        return self.data.columns[0]
    
    def process_smiles_data(self, smiles_column=None):
        """处理SMILES数据"""
        if self.data is None:
            print("请先加载数据")
            return None
            
        if smiles_column is None:
            smiles_column = self.get_smiles_column()
            
        print(f"使用列 '{smiles_column}' 作为SMILES数据")
        
        smiles_sequences = []
        valid_indices = []
        
        for idx, smiles in enumerate(self.data[smiles_column]):
            if pd.isna(smiles) or smiles == '':
                continue
                
            try:
                sequence = smiles_to_sequence(str(smiles))
                smiles_sequences.append(sequence)
                valid_indices.append(idx)
            except Exception as e:
                print(f"处理SMILES '{smiles}' 时出错: {e}")
                continue
        
        if not smiles_sequences:
            print("没有有效的SMILES数据")
            return None, None
            
        smiles_tensor = torch.from_numpy(np.array(smiles_sequences, dtype=int)).long()
        print(f"成功处理 {len(smiles_sequences)} 个SMILES分子")
        
        return smiles_tensor, valid_indices
    
    def create_dummy_graph_data(self, num_molecules):
        """创建虚拟的分子图数据（当没有真实图数据时使用）"""
        # 创建简单的虚拟图数据
        graph_data = np.zeros((num_molecules, Drug_Max_Length, Drug_Max_Length * 2 + 1))
        
        # 为每个分子创建简单的图结构
        for i in range(num_molecules):
            # 距离矩阵（对角线为1，表示自连接）
            np.fill_diagonal(graph_data[i, :, :Drug_Max_Length], 1.0)
            
            # 连接矩阵（简单的线性连接）
            for j in range(min(Drug_Max_Length-1, 10)):  # 只连接前10个原子
                graph_data[i, j, Drug_Max_Length + j + 1] = 1.0
                graph_data[i, j + 1, Drug_Max_Length + j] = 1.0
            
            # 原子特征（随机分配原子类型）
            graph_data[i, :10, -1] = np.random.randint(1, 20, 10)  # 前10个位置有原子
        
        return torch.from_numpy(graph_data).float()
    
    def get_processed_data(self, smiles_column=None):
        """获取处理后的数据"""
        if not self.load_data():
            return None, None, None
            
        smiles_tensor, valid_indices = self.process_smiles_data(smiles_column)
        
        if smiles_tensor is None:
            return None, None, None
        
        # 创建虚拟图数据
        graph_tensor = self.create_dummy_graph_data(len(smiles_tensor))
        
        # 获取有效的原始数据
        valid_data = self.data.iloc[valid_indices].reset_index(drop=True)
        
        return smiles_tensor, graph_tensor, valid_data
    
    def get_data_info(self):
        """获取数据信息"""
        if self.data is None:
            return "未加载数据"
            
        info = f"""
数据信息:
- 总记录数: {len(self.data)}
- 列数: {len(self.data.columns)}
- 列名: {list(self.data.columns)}
- 数据类型: {dict(self.data.dtypes)}
"""
        return info


def create_sample_data():
    """创建示例数据文件"""
    sample_smiles = [
        'CCO',  # 乙醇
        'CC(=O)O',  # 乙酸
        'c1ccccc1',  # 苯
        'CCN(CC)CC',  # 三乙胺
        'CC(C)O',  # 异丙醇
        'CCCCO',  # 丁醇
        'CC(C)(C)O',  # 叔丁醇
        'CCc1ccccc1',  # 乙苯
        'CC(=O)C',  # 丙酮
        'CCCCCCCCCCCCCCCCCC(=O)O'  # 硬脂酸
    ]
    
    sample_data = pd.DataFrame({
        'molecule_id': [f'mol_{i+1:03d}' for i in range(len(sample_smiles))],
        'smiles': sample_smiles,
        'molecular_weight': np.random.uniform(50, 300, len(sample_smiles)),
        'activity': np.random.choice([0, 1], len(sample_smiles))
    })
    
    sample_data.to_excel('sample_input_data.xlsx', index=False)
    print("已创建示例数据文件: sample_input_data.xlsx")
    return sample_data


if __name__ == "__main__":
    # 创建示例数据
    create_sample_data()
    
    # 测试数据处理器
    processor = MoleculeDataProcessor('sample_input_data.xlsx')
    smiles_tensor, graph_tensor, valid_data = processor.get_processed_data()
    
    if smiles_tensor is not None:
        print(f"SMILES张量形状: {smiles_tensor.shape}")
        print(f"图数据张量形状: {graph_tensor.shape}")
        print(f"有效数据条数: {len(valid_data)}")
        print("\n数据预览:")
        print(valid_data.head())
    else:
        print("数据处理失败")
