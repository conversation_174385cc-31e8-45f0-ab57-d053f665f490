# 小分子编码系统

这是一个专门用于小分子编码的深度学习系统，基于SMILES字符串和分子图结构进行特征提取。

## 快速开始

1. 准备输入数据文件 `input_data.xlsx`，包含SMILES列
2. 运行主程序：
   ```bash
   python main_molecule.py
   ```
3. 查看输出文件：`molecule_features.csv`

## 主要文件

- `main_molecule.py`: 主程序
- `molecule_encoder.py`: 分子编码器
- `data_processor.py`: 数据处理器
- `README_molecule.md`: 详细说明文档

## 系统要求
支持Python 3.8+和PyTorch 2.0+，兼容Windows/macOS/Linux系统。

numpy==1.24.4

scikit_learn==1.3.2

torch==2.1.1

tqdm==4.66.1

# Installation Guide
It normally takes about 10 minutes to install a new conda environment on a normal desktop computer. Run the following code under the conda environment to create the new virtual environment and install the required packages.
    
    $ conda create --name DCGCN python=3.8
    
    $ conda activate DCGCN

    $ pip install numpy==1.24.4
    
    $ pip install scikit_learn==1.3.2
    
    $ pip install torch==2.1.1
    
    $ pip install tqdm==4.66.1

# Datasets
We evaluated the performance of the method on two public datasets: DrugBank dataset and Luo's dataset.
For Drugbank dataset, we provided the split datasets for:

(1) ablation experiments in folder **./dataset/drugbank/result/**.

(2) five-fold cross-validation in folder **./dataset/drugbank/result/CV5/**.

(3) cold-start experiments in folder **./dataset/drugbank/result/cold_drug/** and **./dataset/drugbank/result/cold_protein/**.

For Luo's dataset, we provided the split datasets for five-fold cross-validation in folder **./dataset/dtinet/result/CV5/**.

Due to storage space restrictions on github, you can download our dataset by visiting the link: https://drive.google.com/drive/folders/1sy-6yH8VBC6s1LnR8RpEG3WWUg-SW_rP?usp=drive_link

Unzip the dataset folder and place it in the root directory of the project to achieve: ./dataset/

# Training and testing
You can use **main.py** to train the model with **DrugBank and Luo's dataset**. 

Line 4 can assign the GPU devices. 

Line 294 can assign the dataset and the train file manually.

The program can automatically save the best-performing model to the path **./models/**.

After training, you can run **evalute.py** for testing. You need to assign the task and the test set at line 268-273. In addition, you also have to assign the model for test at line 283. In folder **./models/**, we have provided some models that have been trained to help you reproduce the experimental results in the paper.
