import torch
import torch.nn as nn
import numpy as np

# 配置参数
Feature_Size = 256
DSC_Kernel_Num = 32
DSC_Kernel_Size = 8
Drug_SMILES_Input_Size = 128
Drug_Point_Hidden_Size = 512
DPC_Kernel_Num = 32
DPC_Kernel_Size = 8
Drug_Max_Length = 100
Atom_Point_Dict_Length = 79
Atom_Dic_Length = 64
atom_dict = {"#": 29, "%": 30, ")": 31, "(": 1, "+": 32, "-": 33, "/": 34, ".": 2, "1": 35, "0": 3,
            "3": 36, "2": 4, "5": 37, "4": 5, "7": 38, "6": 6, "9": 39, "8": 7, "=": 40, "A": 41,
            "@": 8, "C": 42, "B": 9, "E": 43, "D": 10, "G": 44, "F": 11, "I": 45, "H": 12, "K": 46,
            "<PERSON>": 47, "L": 13, "O": 48, "N": 14, "P": 15, "S": 49, "R": 16, "U": 50, "T": 17, "W": 51,
            "V": 18, "Y": 52, "[": 53, "Z": 19, "]": 54, "\\": 20, "a": 55, "c": 56, "b": 21, "e": 57,
            "d": 22, "g": 58, "f": 23, "i": 59, "h": 24, "m": 60, "l": 25, "o": 61, "n": 26, "s": 62,
            "r": 27, "u": 63, "t": 28, "y": 64}


class DrugSMILESCoding(nn.Module):
    def __init__(self, hid_dim=Drug_SMILES_Input_Size, out_dim=Feature_Size, vocab_size=Atom_Dic_Length,
                 channel=DSC_Kernel_Num, kernel_size=DSC_Kernel_Size):
        super(DrugSMILESCoding, self).__init__()
        self.embedding = nn.Embedding(vocab_size, embedding_dim=hid_dim)
        self.conv1 = nn.Conv1d(hid_dim, channel, kernel_size, padding=kernel_size-1)
        self.conv2 = nn.Conv1d(channel, channel*2, kernel_size, padding=kernel_size-1)
        self.conv3 = nn.Conv1d(channel*2, channel*4, kernel_size, padding=kernel_size-1)
        self.act = nn.LeakyReLU(0.2)
        self.globalmaxpool = nn.AdaptiveMaxPool1d(1)
        self.fc1 = nn.Linear(channel*4, out_dim)

    def forward(self, x):
        x = self.embedding(x)
        x = x.permute(0, 2, 1)
        x = self.conv1(x)
        x = self.act(x)
        x = self.conv2(x)
        x = self.act(x)
        x = self.conv3(x)
        x = self.act(x)
        x = self.globalmaxpool(x)
        x = x.squeeze(-1)
        x = self.fc1(x)
        return x


class DrugPointCoding(nn.Module):
    def __init__(self, point_hid_dim=Drug_Point_Hidden_Size, point_output_dim=Feature_Size,
                 channel=DPC_Kernel_Num, kernel_size=DPC_Kernel_Size):
        super(DrugPointCoding, self).__init__()
        self.gcn1 = nn.Sequential(
            nn.Embedding(Atom_Point_Dict_Length, point_hid_dim),
            nn.Linear(point_hid_dim, point_hid_dim))
        self.gcn2 = nn.Linear(point_hid_dim, point_output_dim)

        self.conv1 = nn.Conv2d(in_channels=2, out_channels=1, kernel_size=1)
        self.conv2 = nn.Conv2d(in_channels=2, out_channels=1, kernel_size=1)

        self.conv = nn.Sequential(
            nn.Conv1d(point_output_dim, channel, kernel_size, padding=kernel_size - 1),
            nn.LeakyReLU(0.2),
            nn.Conv1d(channel, channel * 2, kernel_size, padding=kernel_size - 1),
            nn.LeakyReLU(0.2),
            nn.Conv1d(channel * 2, channel * 4, kernel_size, padding=kernel_size - 1),
            nn.LeakyReLU(0.2),
        )
        self.act = nn.LeakyReLU(0.2)
        self.globalmaxpool = nn.AdaptiveMaxPool1d(1)
        self.fc1 = nn.Linear(channel * 4, point_output_dim)

    def forward(self, xd):
        d_d = xd[:, :, :xd.shape[1]]
        d_c = xd[:, :, xd.shape[1]: xd.shape[1]*2]
        d_a = xd[:, :, -1]
        d_a = torch.squeeze(d_a).long()

        h1 = self.gcn1(d_a)
        h1_1 = torch.einsum('ijk, ikp->ijp', [d_d, h1])
        h1_2 = torch.einsum('ijk, ikp->ijp', [d_c, h1])
        b, r, c = h1_1.shape
        h1_1 = self.act(h1_1).view(b, 1, r, c)
        h1_2 = self.act(h1_2).view(b, 1, r, c)
        h1_12 = torch.cat((h1_1, h1_2), dim=1)
        h1_c = self.conv1(h1_12).view(b, r, c)
        h1 = self.act(h1_c)

        h2 = self.gcn2(h1)
        h2_1 = torch.einsum('ijk, ikp->ijp', [d_d, h2])
        h2_2 = torch.einsum('ijk, ikp->ijp', [d_c, h2])
        b, r, c = h2_1.shape
        h2_1 = self.act(h2_1).view(b, 1, r, c)
        h2_2 = self.act(h2_2).view(b, 1, r, c)
        h2_12 = torch.cat((h2_1, h2_2), dim=1)
        h2_c = self.conv2(h2_12).view(b, r, c)
        h2 = self.act(h2_c)
        h2 = self.act(h2)
        x = h2.permute(0, 2, 1)
        x = self.conv(x)
        x = self.globalmaxpool(x)
        x = x.squeeze(-1)
        x = self.fc1(x)
        return x


class MoleculeEncoder(nn.Module):
    def __init__(self):
        super(MoleculeEncoder, self).__init__()
        self.smiles_encoder = DrugSMILESCoding()
        self.graph_encoder = DrugPointCoding()
        self.fusion_layer = nn.Linear(Feature_Size * 2, Feature_Size)
        self.act = nn.LeakyReLU(0.2)

    def forward(self, smiles_data, graph_data):
        smiles_features = self.smiles_encoder(smiles_data)
        graph_features = self.graph_encoder(graph_data)
        combined_features = torch.cat([smiles_features, graph_features], dim=1)
        output = self.fusion_layer(combined_features)
        output = self.act(output)
        return output, smiles_features, graph_features


def smiles_to_sequence(smiles, max_length=Drug_Max_Length):
    sequence = np.zeros(max_length)
    for i, char in enumerate(smiles[:max_length]):
        if char in atom_dict:
            sequence[i] = atom_dict[char]
    return sequence
