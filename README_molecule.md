# 小分子编码系统

这是一个简化的小分子编码系统，专门用于处理SMILES格式的小分子数据，并生成高维特征表示。

## 功能特点

- **SMILES编码**: 基于1D卷积神经网络的SMILES字符串编码
- **分子图编码**: 基于图卷积网络的分子结构编码  
- **特征融合**: 结合SMILES和图结构信息的多模态特征
- **批量处理**: 支持Excel文件批量处理
- **自动识别**: 自动识别SMILES列名

## 文件结构

```
├── main_molecule.py          # 主程序文件
├── molecule_encoder.py       # 分子编码器模块
├── data_processor.py         # 数据处理模块
├── input_data.xlsx          # 输入数据文件
└── README_molecule.md       # 说明文档
```

## 使用方法

### 1. 准备输入数据

创建Excel文件 `input_data.xlsx`，包含SMILES列。系统会自动识别包含以下关键词的列名：
- smiles, SMILES, Smiles
- smi, SMI
- molecule, structure

示例数据格式：
```
molecule_id | smiles        | other_info
mol_001     | CCO           | 乙醇
mol_002     | CC(=O)O       | 乙酸
mol_003     | c1ccccc1      | 苯
```

### 2. 运行程序

```bash
python main_molecule.py
```

### 3. 输出文件

程序会生成以下文件：
- `molecule_features.csv`: 包含原始数据和编码特征
- `molecule_features_summary.csv`: 处理摘要信息
- `feature_statistics.csv`: 特征统计分析

## 模块说明

### MoleculeEncoder
- **DrugSMILESCoding**: SMILES字符串编码器
- **DrugPointCoding**: 分子图编码器
- **MoleculeEncoder**: 特征融合编码器

### 特征维度
- SMILES特征: 256维
- 图特征: 256维  
- 融合特征: 256维

## 配置参数

可在 `molecule_encoder.py` 中修改以下参数：
- `Feature_Size`: 特征维度 (默认256)
- `Drug_Max_Length`: 最大分子长度 (默认100)
- `DSC_Kernel_Num`: SMILES卷积核数量 (默认32)
- `DPC_Kernel_Num`: 图卷积核数量 (默认32)

## 依赖库

```
torch
pandas
numpy
openpyxl
```

安装依赖：
```bash
pip install torch pandas numpy openpyxl
```

## 注意事项

1. 确保输入的SMILES格式正确
2. 系统会自动创建虚拟图数据（当没有真实图数据时）
3. 支持CPU和GPU运行，会自动检测可用设备
4. 如果没有输入文件，会自动创建示例数据

## 示例用法

```python
from data_processor import MoleculeDataProcessor
from molecule_encoder import MoleculeEncoder

# 处理数据
processor = MoleculeDataProcessor('input_data.xlsx')
smiles_tensor, graph_tensor, valid_data = processor.get_processed_data()

# 编码分子
encoder = MoleculeEncoder()
features, smiles_feat, graph_feat = encoder(smiles_tensor, graph_tensor)
```
